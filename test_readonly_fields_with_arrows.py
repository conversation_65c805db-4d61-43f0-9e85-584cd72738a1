#!/usr/bin/env python3
"""
Test des champs en lecture seule avec boutons flèches et messages de confirmation.
"""
import sys
import os
import tkinter as tk
from tkinter import messagebox

# Add the current directory to the path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.logging_config import configure_logging
from controllers.annotation_controller import AnnotationController


def test_readonly_fields():
    """Test les champs en lecture seule avec boutons flèches."""
    print("Test des champs en lecture seule avec boutons flèches")
    print("=" * 60)
    
    # Configure logging
    configure_logging(log_level='INFO')
    
    # Créer l'application
    controller = AnnotationController([])
    
    # Afficher les instructions
    instructions = """
🔒 TEST DES CHAMPS EN LECTURE SEULE AVEC BOUTONS FLÈCHES

Améliorations testées:

1. 📝 CHAMPS EN LECTURE SEULE:
   • Threshold: Impossible de taper au clavier
   • Transparence: Impossible de taper au clavier  
   • Distance verticale: Impossible de taper au clavier

2. 🔄 BOUTONS FLÈCHES FONCTIONNELS:
   • ▲ pour augmenter de +1
   • ▼ pour diminuer de -1
   • Validation automatique des limites

3. 💬 MESSAGES DE CONFIRMATION:
   • Le titre de la fenêtre affiche brièvement le changement
   • Messages dans les logs
   • Feedback visuel immédiat

4. 📊 SLIDER POUR DISTANCE VERTICALE:
   • Nouveau slider horizontal (0-50+)
   • Synchronisé avec les boutons flèches
   • Extension automatique si dépassement

INSTRUCTIONS DE TEST:
✓ Essayez de taper dans les champs (impossible)
✓ Cliquez sur les boutons ▲/▼ 
✓ Observez les messages dans le titre
✓ Testez le slider de distance verticale
✓ Vérifiez les limites automatiques

Fermez l'application quand vous avez terminé.
    """
    
    messagebox.showinfo("Instructions de test", instructions)
    
    print("Interface lancée avec les améliorations:")
    print("• Champs en lecture seule")
    print("• Boutons flèches avec messages")
    print("• Slider pour distance verticale")
    print("• Validation automatique des limites")
    
    try:
        # Lancer l'application
        controller.run()
        print("\nTest terminé avec succès!")
        return True
    except Exception as e:
        print(f"Erreur: {e}")
        return False


def main():
    """Fonction principale."""
    print("🚀 Test des améliorations d'interface")
    print("\nFonctionnalités testées:")
    print("🔒 Champs en lecture seule (pas de modification clavier)")
    print("🔄 Boutons flèches avec incrémentation ±1")
    print("💬 Messages de confirmation dans le titre")
    print("📊 Slider pour distance verticale")
    print("✅ Validation automatique des limites")
    print("🎯 Interface utilisateur améliorée")
    print()
    
    success = test_readonly_fields()
    
    if success:
        print("✅ Test réussi!")
        print("\nAméliorations validées:")
        print("• Impossible de modifier les champs au clavier")
        print("• Boutons flèches fonctionnels avec feedback")
        print("• Messages de confirmation visibles")
        print("• Slider de distance verticale opérationnel")
    else:
        print("❌ Erreur pendant le test!")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
