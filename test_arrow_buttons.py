#!/usr/bin/env python3
"""
Test script pour vérifier que les boutons flèches fonctionnent correctement.
"""
import sys
import os
import tkinter as tk
from tkinter import messagebox

# Add the current directory to the path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.logging_config import configure_logging
from controllers.annotation_controller import AnnotationController


def test_arrow_buttons():
    """Test les boutons flèches dans l'interface."""
    print("Test des boutons flèches d'incrémentation/décrémentation")
    print("=" * 60)
    
    # Configure logging
    configure_logging(log_level='INFO')
    
    # Créer l'application
    root = tk.Tk()
    root.title("Test des boutons flèches")
    
    # Créer le contrôleur
    controller = AnnotationController([])
    
    # Afficher un message d'instructions
    instructions = """
    Test des boutons flèches ajoutés:
    
    1. <PERSON><PERSON><PERSON><PERSON> (0-255): Boutons ▲/▼ à côté du champ de saisie
    2. Transparence (0-100): Boutons ▲/▼ à côté du champ de saisie  
    3. Distance verticale: Boutons ▲/▼ à côté du champ de saisie
    
    Instructions:
    - Cliquez sur les boutons ▲ pour incrémenter de +1
    - Cliquez sur les boutons ▼ pour décrémenter de -1
    - Les valeurs sont automatiquement limitées aux plages valides
    - Les changements sont appliqués en temps réel
    
    Fermez cette fenêtre pour continuer le test.
    """
    
    messagebox.showinfo("Instructions de test", instructions)
    
    print("Interface lancée avec les boutons flèches.")
    print("Testez les fonctionnalités suivantes:")
    print("- Boutons ▲/▼ pour Threshold")
    print("- Boutons ▲/▼ pour Transparence") 
    print("- Boutons ▲/▼ pour Distance verticale")
    print("- Vérifiez que les limites sont respectées")
    print("- Vérifiez que les mises à jour sont en temps réel")
    
    try:
        # Lancer l'application
        controller.run()
        print("Test terminé avec succès!")
        return True
    except Exception as e:
        print(f"Erreur pendant le test: {e}")
        return False


def main():
    """Fonction principale."""
    print("Test des boutons flèches d'incrémentation")
    print("Fonctionnalités testées:")
    print("✓ Boutons ▲/▼ pour Threshold (0-255)")
    print("✓ Boutons ▲/▼ pour Transparence (0-100)")
    print("✓ Boutons ▲/▼ pour Distance verticale (≥0)")
    print("✓ Validation des limites")
    print("✓ Mise à jour en temps réel")
    print()
    
    success = test_arrow_buttons()
    
    if success:
        print("\n✓ Test des boutons flèches réussi!")
    else:
        print("\n✗ Test des boutons flèches échoué!")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
