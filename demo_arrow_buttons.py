#!/usr/bin/env python3
"""
Démonstration des boutons flèches ajoutés à l'interface.
"""
import sys
import os
import tkinter as tk
from tkinter import messagebox

# Add the current directory to the path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.logging_config import configure_logging
from controllers.annotation_controller import AnnotationController


def demo_arrow_buttons():
    """Démonstration des boutons flèches."""
    print("Démonstration des boutons flèches")
    print("=" * 50)
    
    # Configure logging
    configure_logging(log_level='INFO')
    
    # Créer l'application
    controller = AnnotationController([])
    
    # Afficher les instructions
    instructions = """
🎯 DÉMONSTRATION DES BOUTONS FLÈCHES

Nouveaux boutons ajoutés à l'interface:

📊 THRESHOLD (0-255):
   • Boutons ▲/▼ à côté du champ de saisie
   • Incrémente/décrémente par pas de 1
   • Limites automatiques: 0 minimum, 255 maximum

🔍 TRANSPARENCE (0-100%):
   • Boutons ▲/▼ à côté du champ de saisie  
   • Incrémente/décrémente par pas de 1
   • Limites automatiques: 0% minimum, 100% maximum

📏 DISTANCE VERTICALE (pixels):
   • Boutons ▲/▼ à côté du champ de saisie
   • Incrémente/décrémente par pas de 1
   • Limite automatique: 0 minimum, pas de maximum

✨ FONCTIONNALITÉS:
   • Mise à jour en temps réel des masques
   • Validation automatique des limites
   • Interface cohérente avec les sliders existants
   • Boutons compacts et intuitifs

Testez les boutons et fermez l'application quand vous avez terminé.
    """
    
    messagebox.showinfo("Démonstration", instructions)
    
    print("Interface lancée avec les nouveaux boutons flèches.")
    print("\nTestez les fonctionnalités:")
    print("• Cliquez sur ▲ pour augmenter de 1")
    print("• Cliquez sur ▼ pour diminuer de 1") 
    print("• Observez les limites automatiques")
    print("• Vérifiez la mise à jour en temps réel")
    
    try:
        # Lancer l'application
        controller.run()
        print("\nDémonstration terminée!")
        return True
    except Exception as e:
        print(f"Erreur: {e}")
        return False


def main():
    """Fonction principale."""
    print("🚀 Démonstration des boutons flèches d'incrémentation")
    print("\nAméliorations apportées:")
    print("✅ Boutons ▲/▼ pour Threshold")
    print("✅ Boutons ▲/▼ pour Transparence")
    print("✅ Boutons ▲/▼ pour Distance verticale")
    print("✅ Validation automatique des limites")
    print("✅ Mise à jour en temps réel")
    print("✅ Interface utilisateur améliorée")
    print()
    
    success = demo_arrow_buttons()
    
    if success:
        print("✅ Démonstration réussie!")
    else:
        print("❌ Erreur pendant la démonstration!")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
