#!/usr/bin/env python3
"""
Test script to verify that Unicode logging works correctly.
"""
import logging
import sys
import os

# Add the current directory to the path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.logging_config import configure_logging


def test_unicode_logging():
    """Test that Unicode characters in log messages work correctly."""
    print("Testing Unicode logging configuration...")
    
    # Configure logging with our fixed configuration
    configure_logging(log_level='INFO')
    
    # Get a logger
    logger = logging.getLogger(__name__)
    
    # Test various Unicode characters that were causing issues
    test_messages = [
        "Index de label mis à jour: 0 -> label courant = frontwall",  # Fixed arrow
        "[OK] JSON sauvé dans : test/folder",  # Fixed checkmark
        "[ERREUR] Erreur export JSON : test error",  # Fixed cross mark
        "Test avec caractères français: éàèùç",  # French characters
        "Test normal ASCII message",  # Normal ASCII
    ]
    
    print("\nTesting log messages:")
    for i, message in enumerate(test_messages, 1):
        try:
            logger.info(f"Test {i}: {message}")
            print(f"✓ Test {i} passed")
        except UnicodeEncodeError as e:
            print(f"✗ Test {i} failed with UnicodeEncodeError: {e}")
            return False
        except Exception as e:
            print(f"✗ Test {i} failed with unexpected error: {e}")
            return False
    
    print("\n✓ All Unicode logging tests passed!")
    return True


def test_model_logging():
    """Test the specific logging that was causing issues in mask_model.py"""
    print("\nTesting mask_model style logging...")
    
    logger = logging.getLogger("models.mask_model")
    
    # Test the exact pattern that was failing
    try:
        value = 1
        current_label = "backwall"
        logger.info(f"Index de label mis à jour: {value} -> label courant = {current_label}")
        print("✓ Mask model logging test passed")
        return True
    except UnicodeEncodeError as e:
        print(f"✗ Mask model logging test failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Mask model logging test failed with unexpected error: {e}")
        return False


def main():
    """Main test function."""
    print("=" * 60)
    print("Unicode Logging Fix Test")
    print("=" * 60)
    
    # Test basic Unicode logging
    if not test_unicode_logging():
        print("\n❌ Unicode logging tests failed!")
        return False
    
    # Test specific model logging
    if not test_model_logging():
        print("\n❌ Model logging tests failed!")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 All tests passed! Unicode logging fix is working correctly.")
    print("=" * 60)
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
