#!/usr/bin/env python3
"""
Test unitaire pour vérifier les fonctions d'incrémentation.
"""
import sys
import os
import tkinter as tk

# Add the current directory to the path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.logging_config import configure_logging
from views.annotation_view import AnnotationView
from controllers.annotation_controller import AnnotationController


def test_increment_functions():
    """Test les fonctions d'incrémentation sans interface graphique."""
    print("Test des fonctions d'incrémentation...")
    
    # Configure logging
    configure_logging(log_level='INFO')
    
    # Créer une fenêtre Tkinter minimale
    root = tk.Tk()
    root.withdraw()  # Cacher la fenêtre
    
    try:
        # C<PERSON>er le contrôleur et la vue
        controller = AnnotationController([])
        view = controller._view
        
        # Test 1: Incrémentation du threshold
        print("\n1. Test threshold:")
        initial_threshold = view._threshold_var.get()
        print(f"   Valeur initiale: {initial_threshold}")
        
        # Test incrémentation positive
        view._increment_threshold(5)
        new_value = view._threshold_var.get()
        print(f"   Après +5: {new_value}")
        assert new_value == initial_threshold + 5, f"Attendu {initial_threshold + 5}, obtenu {new_value}"
        
        # Test incrémentation négative
        view._increment_threshold(-3)
        new_value = view._threshold_var.get()
        print(f"   Après -3: {new_value}")
        assert new_value == initial_threshold + 2, f"Attendu {initial_threshold + 2}, obtenu {new_value}"
        
        # Test limite supérieure
        view._threshold_var.set(254)
        view._increment_threshold(5)
        new_value = view._threshold_var.get()
        print(f"   Test limite max (254+5): {new_value}")
        assert new_value == 255, f"Attendu 255, obtenu {new_value}"
        
        # Test limite inférieure
        view._threshold_var.set(2)
        view._increment_threshold(-5)
        new_value = view._threshold_var.get()
        print(f"   Test limite min (2-5): {new_value}")
        assert new_value == 0, f"Attendu 0, obtenu {new_value}"
        
        print("   ✓ Threshold: Tous les tests passés")
        
        # Test 2: Incrémentation de l'alpha
        print("\n2. Test transparence:")
        view._alpha_var.set(50)
        initial_alpha = view._alpha_var.get()
        print(f"   Valeur initiale: {initial_alpha}")
        
        # Test incrémentation positive
        view._increment_alpha(10)
        new_value = view._alpha_var.get()
        print(f"   Après +10: {new_value}")
        assert new_value == 60, f"Attendu 60, obtenu {new_value}"
        
        # Test limite supérieure
        view._alpha_var.set(98)
        view._increment_alpha(5)
        new_value = view._alpha_var.get()
        print(f"   Test limite max (98+5): {new_value}")
        assert new_value == 100, f"Attendu 100, obtenu {new_value}"
        
        # Test limite inférieure
        view._alpha_var.set(3)
        view._increment_alpha(-5)
        new_value = view._alpha_var.get()
        print(f"   Test limite min (3-5): {new_value}")
        assert new_value == 0, f"Attendu 0, obtenu {new_value}"
        
        print("   ✓ Transparence: Tous les tests passés")
        
        # Test 3: Incrémentation de la distance verticale
        print("\n3. Test distance verticale:")
        view._vertical_gap_var.set(10)
        initial_gap = view._vertical_gap_var.get()
        print(f"   Valeur initiale: {initial_gap}")
        
        # Test incrémentation positive
        view._increment_vertical_gap(5)
        new_value = view._vertical_gap_var.get()
        print(f"   Après +5: {new_value}")
        assert new_value == 15, f"Attendu 15, obtenu {new_value}"
        
        # Test limite inférieure
        view._vertical_gap_var.set(2)
        view._increment_vertical_gap(-5)
        new_value = view._vertical_gap_var.get()
        print(f"   Test limite min (2-5): {new_value}")
        assert new_value == 0, f"Attendu 0, obtenu {new_value}"
        
        # Test valeurs élevées (pas de limite max)
        view._vertical_gap_var.set(100)
        view._increment_vertical_gap(50)
        new_value = view._vertical_gap_var.get()
        print(f"   Test valeur élevée (100+50): {new_value}")
        assert new_value == 150, f"Attendu 150, obtenu {new_value}"
        
        print("   ✓ Distance verticale: Tous les tests passés")
        
        print("\n✓ Tous les tests d'incrémentation ont réussi!")
        return True
        
    except Exception as e:
        print(f"\n✗ Erreur pendant les tests: {e}")
        return False
    finally:
        root.destroy()


def main():
    """Fonction principale."""
    print("=" * 60)
    print("Test unitaire des fonctions d'incrémentation")
    print("=" * 60)
    
    success = test_increment_functions()
    
    if success:
        print("\n🎉 Tous les tests ont réussi!")
        print("\nFonctionnalités validées:")
        print("✓ _increment_threshold() avec validation des limites (0-255)")
        print("✓ _increment_alpha() avec validation des limites (0-100)")
        print("✓ _increment_vertical_gap() avec validation des limites (≥0)")
        print("✓ Gestion correcte des dépassements de limites")
    else:
        print("\n❌ Certains tests ont échoué!")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
