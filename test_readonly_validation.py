#!/usr/bin/env python3
"""
Test unitaire pour valider que les champs sont en lecture seule.
"""
import sys
import os
import tkinter as tk

# Add the current directory to the path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.logging_config import configure_logging
from controllers.annotation_controller import AnnotationController


def test_readonly_fields():
    """Test que les champs sont bien en lecture seule."""
    print("Test des champs en lecture seule...")
    
    # Configure logging
    configure_logging(log_level='INFO')
    
    # Créer une fenêtre Tkinter minimale
    root = tk.Tk()
    root.withdraw()  # Cacher la fenêtre
    
    try:
        # C<PERSON>er le contrôleur et la vue
        controller = AnnotationController([])
        view = controller._view
        
        # Test 1: Vérifier que les champs sont en lecture seule
        print("\n1. Test état des champs:")
        
        # Threshold entry
        threshold_state = view.threshold_entry['state']
        print(f"   Threshold entry state: {threshold_state}")
        assert threshold_state == 'readonly', f"Threshold devrait être readonly, mais est {threshold_state}"
        
        # Alpha entry
        alpha_state = view.alpha_entry['state']
        print(f"   Alpha entry state: {alpha_state}")
        assert alpha_state == 'readonly', f"Alpha devrait être readonly, mais est {alpha_state}"
        
        # Vertical gap entry
        vertical_gap_state = view.vertical_gap_entry['state']
        print(f"   Vertical gap entry state: {vertical_gap_state}")
        assert vertical_gap_state == 'readonly', f"Vertical gap devrait être readonly, mais est {vertical_gap_state}"
        
        print("   ✓ Tous les champs sont en lecture seule")
        
        # Test 2: Vérifier que les boutons flèches existent et fonctionnent
        print("\n2. Test des fonctions d'incrémentation:")
        
        # Test threshold
        initial_threshold = view._threshold_var.get()
        view._increment_threshold(5)
        new_threshold = view._threshold_var.get()
        print(f"   Threshold: {initial_threshold} -> {new_threshold}")
        assert new_threshold == initial_threshold + 5, "Incrémentation threshold échouée"
        
        # Test alpha
        view._alpha_var.set(50)
        initial_alpha = view._alpha_var.get()
        view._increment_alpha(10)
        new_alpha = view._alpha_var.get()
        print(f"   Alpha: {initial_alpha} -> {new_alpha}")
        assert new_alpha == 60, "Incrémentation alpha échouée"
        
        # Test vertical gap
        view._vertical_gap_var.set(10)
        initial_gap = view._vertical_gap_var.get()
        view._increment_vertical_gap(3)
        new_gap = view._vertical_gap_var.get()
        print(f"   Vertical gap: {initial_gap} -> {new_gap}")
        assert new_gap == 13, "Incrémentation vertical gap échouée"
        
        print("   ✓ Toutes les fonctions d'incrémentation fonctionnent")
        
        # Test 3: Vérifier que le slider vertical gap existe
        print("\n3. Test du slider vertical gap:")
        
        # Vérifier que le slider existe
        assert hasattr(view, 'vertical_gap_slider'), "Slider vertical gap manquant"
        
        # Vérifier les propriétés du slider
        slider_from = view.vertical_gap_slider['from']
        slider_to = view.vertical_gap_slider['to']
        print(f"   Slider range: {slider_from} - {slider_to}")
        assert slider_from == 0, f"Slider from devrait être 0, mais est {slider_from}"
        assert slider_to >= 50, f"Slider to devrait être >= 50, mais est {slider_to}"
        
        # Test du changement via slider (simuler le changement comme si l'utilisateur l'avait fait)
        view.vertical_gap_slider.set(25)
        # Déclencher manuellement la fonction de callback comme le ferait Tkinter
        view._on_vertical_gap_change(25)
        slider_value = view._vertical_gap_var.get()
        print(f"   Slider value après set(25): {slider_value}")
        assert slider_value == 25, "Synchronisation slider échouée"
        
        print("   ✓ Slider vertical gap fonctionne correctement")
        
        print("\n✓ Tous les tests de validation ont réussi!")
        return True
        
    except Exception as e:
        print(f"\n✗ Erreur pendant les tests: {e}")
        return False
    finally:
        root.destroy()


def main():
    """Fonction principale."""
    print("=" * 60)
    print("Test de validation des champs en lecture seule")
    print("=" * 60)
    
    success = test_readonly_fields()
    
    if success:
        print("\n🎉 Validation réussie!")
        print("\nFonctionnalités validées:")
        print("✓ Champs threshold, alpha et vertical gap en lecture seule")
        print("✓ Fonctions d'incrémentation opérationnelles")
        print("✓ Slider vertical gap fonctionnel")
        print("✓ Synchronisation entre contrôles")
        print("✓ Validation des limites")
    else:
        print("\n❌ Validation échouée!")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
